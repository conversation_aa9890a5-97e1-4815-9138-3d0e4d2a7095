"use client";
import { Col, Row, Accordion } from "react-bootstrap";
import React, { useState, useEffect } from "react";
import AccountLayout from "@/Layouts/AccountLayout";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import CommonBlackCard from "@/Components/common/Account/CommonBlackCard";
import MetaHead from "@/Seo/Meta/MetaHead";
import CommonButton from "@/Components/UI/CommonButton";
import CommonHeading from "@/Components/UI/CommonHeading";
import "@/css/Home/FaqCard.scss";
import Cookies from "js-cookie";

export default function Subscriptions() {
  const [loginToken, setLoginToken] = useState(null);
  const [currentPlan, setCurrentPlan] = useState("Essential Plan");
  const [accountStatus, setAccountStatus] = useState("SUSPENDED");
  const [renewalDate, setRenewalDate] = useState("MAY 3, 2024");
  const [billingCycle, setBillingCycle] = useState("Monthly");

  // Mock subscription plans data - in real app this would come from API
  const subscriptionPlans = [
    {
      id: 1,
      title: "Free Plan",
      price: "$0",
      billing_type: "free",
      order: 1,
    },
    {
      id: 2,
      title: "Essential Plan",
      price: "$12.95",
      monthlyPrice: "$12.95",
      yearlyPrice: "$155.40",
      billing_type: "monthly",
      order: 2,
    },
    {
      id: 3,
      title: "Plus Plan",
      price: "$24.95",
      monthlyPrice: "$24.95",
      yearlyPrice: "$299.40",
      billing_type: "monthly",
      order: 3,
    },
    {
      id: 4,
      title: "Premium Plan",
      price: "$39.95",
      monthlyPrice: "$39.95",
      yearlyPrice: "$479.40",
      billing_type: "monthly",
      order: 4,
    },
  ];

  useEffect(() => {
    const tokens = Cookies.get("authToken");
    setLoginToken(tokens || null);
  }, []);

  // Button styling logic based on plan status (similar to PricingClient.js)
  const getButtonProps = (plan) => {
    let buttonTitle = "";
    let buttonClass = "";
    let buttonSubTitle = "";
    let actionType = "";

    if (plan.title === currentPlan) {
      buttonTitle = "Current Plan";
      buttonSubTitle = "Change Billing Cycle";
      buttonClass = "gray-btn";
    } else if (plan.billing_type === "free") {
      buttonTitle = "Downgrade To Free";
      buttonClass = "yellow-btn";
      actionType = "downgrade";
    } else if (
      plan.order > subscriptionPlans.find((p) => p.title === currentPlan)?.order
    ) {
      buttonTitle = `Upgrade To ${plan.title}`;
      buttonClass = "green-btn";
      actionType = "upgrade";
    } else {
      buttonTitle = `Downgrade To ${plan.title}`;
      buttonClass = "yellow-btn";
      actionType = "downgrade";
    }

    return { buttonTitle, buttonClass, buttonSubTitle, actionType };
  };

  const handlePlanAction = (plan, actionType) => {
    // Handle plan changes - in real app this would call API
    console.log(`${actionType} to ${plan.title}`);
  };

  const metaArray = {
    noindex: true,
    title: "Account Subscriptions | Manage Plans | TradeReply",
    description:
      "Check your subscription status on TradeReply.com. View your current plan, manage upgrades, and review billing details.",
    canonical_link: "https://www.tradereply.com/account/subscriptions",
    og_site_name: "TradeReply",
    og_title: "Subscription Status | Manage Plans | TradeReply",
    og_description:
      "View and manage your subscription plans on TradeReply. Upgrade, downgrade, or cancel your subscription with ease.",
    twitter_title: "Subscription Status | Manage Plans | TradeReply",
    twitter_description:
      "View and manage your subscription plans on TradeReply. Upgrade, downgrade, or cancel your subscription with ease.",
  };
  return (
    <>
      <AccountLayout>
        <MetaHead props={metaArray} />
        <div className="subscriptions_sec">
          <SidebarHeading title="Subscriptions" />

          {/* Subscription Plans Table */}
          <Row className="mb-4 mb-lg-5">
            <Col xs={12}>
              <CommonBlackCard title="Subscriptions" className="account_card">
                <div className="account_card_table">
                  <table className="w-full md:min-w-[800px]">
                    <tbody>
                      {subscriptionPlans.map((plan) => {
                        const {
                          buttonTitle,
                          buttonClass,
                          buttonSubTitle,
                          actionType,
                        } = getButtonProps(plan);
                        const isCurrentPlan = plan.title === currentPlan;

                        // Generate renewal information for the renewal column (current plan only)
                        const getRenewalInfo = () => {
                          if (isCurrentPlan) {
                            return (
                              <div className="text-white">
                                <div className="fw-bold text-xs sm:text-sm lg:text-base leading-tight">
                                  {renewalDate}
                                </div>
                                <small className="text-white text-xs sm:text-sm">
                                  Renewal
                                </small>
                              </div>
                            );
                          }
                          return null;
                        };

                        // Generate billing information for the billing cycle column
                        const getBillingInfo = () => {
                          if (isCurrentPlan) {
                            return (
                              <div className="text-center md:text-left">
                                <div className="text-white fw-bold text-xs sm:text-sm lg:text-base">
                                  {billingCycle}
                                </div>
                                <small className="text-xs sm:text-sm d-block">
                                  Billing Cycle
                                </small>
                              </div>
                            );
                          } else if (plan.billing_type === "free") {
                            return (
                              <div className="text-white">
                                <div className="fw-bold text-xs sm:text-sm lg:text-base">
                                  $0
                                </div>
                              </div>
                            );
                          } else {
                            return (
                              <div className="text-white">
                                <div className="fw-bold text-xs sm:text-sm lg:text-base leading-tight">
                                  {plan.monthlyPrice} monthly
                                </div>
                                <div className="text-xs leading-tight text-gray-300">
                                  {plan.yearlyPrice} annually
                                </div>
                              </div>
                            );
                          }
                        };

                        return (
                          <tr
                            className="flex flex-col items-center space-y-4
              border-b border-gray-600 py-4
              md:table-row md:flex-row md:space-y-0 md:py-0"
                            key={plan.id}
                          >
                            {/* TradeReply Logo Column */}
                            <td className="align-top py-2 px-1 sm:py-3 sm:px-2 text-center md:text-left lg:px-4 min-w-[80px]">
                              <h6 className="mb-0 text-xs sm:text-sm lg:text-base whitespace-nowrap">
                                Trade<span className="blue_text">Reply</span>
                              </h6>
                            </td>

                            {/* Plan Name Column */}
                            <td className="align-top py-2 px-1 sm:py-3 sm:px-2 text-center md:text-left lg:px-4 min-w-[100px]">
                              <span className="text-white fw-bold text-xs sm:text-sm lg:text-base">
                                {plan.title}
                              </span>
                            </td>

                            {/* Account Status Column */}
                            <td className="align-top py-2 px-1 sm:py-3 sm:px-2 lg:px-4 min-w-[120px]">
                              {isCurrentPlan && (
                                <div className="text-center md:text-left">
                                  <div className="yellow_text fw-bold text-xs sm:text-sm lg:text-base">
                                    {accountStatus}
                                  </div>
                                  <div className="text-white text-xs sm:text-sm lg:text-base">
                                    Account Status
                                  </div>
                                </div>
                              )}
                            </td>

                            {/* Renewal Column (Current Plan Only) */}
                            <td className="align-top py-2 px-1 sm:py-3 sm:px-2 text-center md:text-left lg:px-4 min-w-[120px]">
                              {getRenewalInfo()}
                            </td>

                            {/* Billing Information Column */}
                            <td className="align-top py-2 px-1 sm:py-3 sm:px-2 lg:px-4 text-center md:text-left min-w-[140px]">
                              {getBillingInfo()}
                            </td>

                            {/* Action Button Column */}
                            <td className="align-top py-2 px-1 sm:py-3 sm:px-2 lg:px-4 text-center md:text-left min-w-[160px] max-w-[200px]">
                              <CommonButton
                                onClick={() =>
                                  handlePlanAction(plan, actionType)
                                }
                                title={buttonTitle}
                                subtitle={buttonSubTitle}
                                disabled={buttonTitle.includes("Current Plan")}
                                className={`subscription-button w-full max-w-[160px] sm:max-w-[180px] lg:max-w-[200px] ${buttonClass} text-xs leading-tight`}
                                style={{
                                  ...(buttonTitle.includes("Current Plan")
                                    ? { cursor: "not-allowed", opacity: 0.6 }
                                    : {}),
                                }}
                              />
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </CommonBlackCard>
            </Col>
          </Row>

          {/* FAQ Section */}
          <Row className="mb-4 mb-lg-5">
            <Col xs={12}>
              <div className="faq_card">
                <CommonHeading heading="Frequently Asked Questions" />
                <div className="faq_card_accordion">
                  <Accordion defaultActiveKey="0">
                    <Accordion.Item eventKey="1">
                      <Accordion.Header>
                        Do I get charged during the free trial?
                      </Accordion.Header>
                      <Accordion.Body>
                        <div>
                          No, the 30-day trial is completely free. You won't be
                          charged unless you keep your paid plan after the trial
                          ends. Cancel or downgrade before your trial expires to
                          avoid being billed.
                        </div>
                      </Accordion.Body>
                    </Accordion.Item>

                    <Accordion.Item eventKey="2">
                      <Accordion.Header>
                        What happens if I upgrade my plan mid-cycle?
                      </Accordion.Header>
                      <Accordion.Body>
                        <div>
                          Upgrades take effect immediately. You'll gain access
                          to premium features right away and be charged a
                          prorated amount for the rest of your billing period.
                        </div>
                      </Accordion.Body>
                    </Accordion.Item>

                    <Accordion.Item eventKey="3">
                      <Accordion.Header>
                        What happens if I downgrade my plan?
                      </Accordion.Header>
                      <Accordion.Body>
                        <div>
                          Downgrades take effect at the end of your current
                          billing cycle. You'll retain access to your current
                          plan's features until then. If you're on a free trial,
                          the downgrade takes effect when the trial ends.
                        </div>
                      </Accordion.Body>
                    </Accordion.Item>

                    <Accordion.Item eventKey="4">
                      <Accordion.Header>
                        Can I cancel my subscription at any time?
                      </Accordion.Header>
                      <Accordion.Body>
                        <div>
                          Yes. You can cancel anytime in your account settings.
                          Your current plan will stay active through the end of
                          the billing period. After that, your account will move
                          to the Free plan unless you choose otherwise.
                        </div>
                      </Accordion.Body>
                    </Accordion.Item>

                    <Accordion.Item eventKey="5">
                      <Accordion.Header>
                        Will I lose access to my trade history if I'm on the
                        Free plan?
                      </Accordion.Header>
                      <Accordion.Body>
                        <div>
                          No, your full trade history is securely stored.
                          However, Free plan users can only view the most recent
                          6 months of trades. Older entries are hidden unless
                          you upgrade to a paid plan.
                        </div>
                      </Accordion.Body>
                    </Accordion.Item>

                    <Accordion.Item eventKey="6">
                      <Accordion.Header>
                        Can I get a refund if I cancel?
                      </Accordion.Header>
                      <Accordion.Body>
                        <div>
                          Annual plans: Full refund if canceled within 14 days.
                          <br />
                          Monthly plans: Non-refundable, but contact support if
                          you believe an exception should apply. Refunds are not
                          issued for upgrades or users who file chargebacks.
                        </div>
                      </Accordion.Body>
                    </Accordion.Item>

                    <Accordion.Item eventKey="7">
                      <Accordion.Header>
                        What happens if my payment fails during renewal?
                      </Accordion.Header>
                      <Accordion.Body>
                        <div>
                          If you're on an annual plan, we'll automatically
                          switch you to a monthly plan. If payment fails again,
                          your subscription will be canceled, and you'll be
                          moved to the Free plan.
                        </div>
                      </Accordion.Body>
                    </Accordion.Item>

                    <Accordion.Item eventKey="8">
                      <Accordion.Header>
                        Can I start another free trial if I cancel?
                      </Accordion.Header>
                      <Accordion.Body>
                        <div>
                          No. Free trials are limited to one per account. If you
                          cancel during your trial, you'll keep access until the
                          30 days are up, but you won't be eligible for another
                          trial in the future.
                        </div>
                      </Accordion.Body>
                    </Accordion.Item>

                    <Accordion.Item eventKey="9">
                      <Accordion.Header>
                        Are taxes included in the subscription price?
                      </Accordion.Header>
                      <Accordion.Body>
                        <div>
                          Taxes are added based on your billing location.
                          TradeReply collects sales tax where required by law.
                          You'll see the tax breakdown during checkout.
                        </div>
                      </Accordion.Body>
                    </Accordion.Item>

                    <Accordion.Item eventKey="10">
                      <Accordion.Header>
                        What payment methods do you accept?
                      </Accordion.Header>
                      <Accordion.Body>
                        <div>
                          We accept all major credit and debit cards. By adding
                          a payment method, you authorize TradeReply to
                          automatically bill it based on your selected plan and
                          billing cycle.
                        </div>
                      </Accordion.Body>
                    </Accordion.Item>
                  </Accordion>
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </AccountLayout>
    </>
  );
}
