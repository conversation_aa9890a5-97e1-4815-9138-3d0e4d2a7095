'use client';
import { Col, Row } from "react-bootstrap";
import React, { useState, useEffect } from 'react';
import AccountLayout from "@/Layouts/AccountLayout";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import { CheckIcon } from "@/assets/svgIcons/SvgIcon";
import "@/css/account/Security.scss";
import "@/css/account/AccountDetails.scss";
import MetaHead from "@/Seo/Meta/MetaHead";
import RestoreCodeModal from "../partial/RestoreCodeModal";
import ConfirmDisable2FAModal from "../partial/ConfirmDisable2FAModal";
import ConfirmGenerateRestoreCodeModal from "../partial/ConfirmGenerateRestoreCodeModal";
import StatusIndicator from '@/Components/UI/StatusIndicator';
import CommonTooltip from '@/Components/UI/CommonTooltip';
import { get2FAStatus, enable2FA, disable2FA, generateRestoreCode, update2FAAlwaysRequired, get } from "@/utils/apiUtils";
import { useSecurityCookieMonitor } from "@/Hooks/useSecurityCookieMonitor";
import { useSelector, useDispatch } from 'react-redux';
import { setUser } from '@/redux/authSlice';
import Cookies from 'js-cookie';

export default function Security() {
    const [is2FAEnabled, setIs2FAEnabled] = useState(false);
    const [isAlwaysRequire, setIsAlwaysRequire] = useState(false);
    const [showRestoreModal, setShowRestoreModal] = useState(false);
    const [restoreCode, setRestoreCode] = useState('');
    const [showDisableModal, setShowDisableModal] = useState(false);
    const [showGenerateWarningModal, setShowGenerateWarningModal] = useState(false);
    const [copied, setCopied] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [isLoadingUserData, setIsLoadingUserData] = useState(true);
    const [error, setError] = useState(null);
    const [originalStatus, setOriginalStatus] = useState(false);
    const [saveStatus, setSaveStatus] = useState(null);
    const [isGeneratingCode, setIsGeneratingCode] = useState(false);

    // Security cookie monitoring
    useSecurityCookieMonitor();

    // Get user data from Redux
    const reduxUser = useSelector((state) => state?.auth?.user || null);
    const dispatch = useDispatch();

    const defaultState = {
        is2FAEnabled: false,
        isAlwaysRequire: false
    };

    // Fetch user data to ensure we have email
    const fetchUserData = async () => {
        try {
            const response = await get('/account');
            if (response.success && response.data) {
                console.log('Fetched fresh user data:', response.data);
                dispatch(setUser(response.data));
                localStorage.setItem('user', JSON.stringify(response.data));
                return response.data;
            }
        } catch (error) {
            console.error('Error fetching user data:', error);
            // Fallback to localStorage data if API fails
            const storedUser = localStorage.getItem('user');
            if (storedUser) {
                try {
                    const parsedUser = JSON.parse(storedUser);
                    console.log('Using stored user data as fallback:', parsedUser);
                    dispatch(setUser(parsedUser));
                    return parsedUser;
                } catch (parseError) {
                    console.error('Error parsing stored user data:', parseError);
                }
            }
        }
        return null;
    };

    // Fetch current 2FA status from backend
    const fetch2FAStatus = async () => {
        try {
            setIsLoadingUserData(true);
            setError(null);

            // Fetch both 2FA status and user data
            const [twoFAResponse] = await Promise.all([
                get2FAStatus(),
                fetchUserData() // Ensure we have fresh user data including email
            ]);

            if (twoFAResponse.success && twoFAResponse.data) {
                const enabled = twoFAResponse.data.two_factor_enabled || false;
                const alwaysRequired = twoFAResponse.data.two_factor_always_required || false;
                setIs2FAEnabled(enabled);
                setOriginalStatus(enabled);
                setIsAlwaysRequire(alwaysRequired);
            }
        } catch (error) {
            console.error('Error fetching 2FA status:', error);
            setError('Failed to load 2FA status');
        } finally {
            setIsLoadingUserData(false);
        }
    };

    // Initialize Redux state from localStorage if needed
    const initializeUserData = () => {
        try {
            const authToken = Cookies.get('authToken');
            const storedUser = localStorage.getItem('user');

            console.log('Auth token exists:', !!authToken);
            console.log('Stored user exists:', !!storedUser);
            console.log('Redux user exists:', !!reduxUser);

            if (storedUser && !reduxUser) {
                const parsedUser = JSON.parse(storedUser);
                console.log('Loading user from localStorage:', parsedUser);
                dispatch(setUser(parsedUser));
                return parsedUser;
            }
            return reduxUser;
        } catch (error) {
            console.error('Error parsing stored user data:', error);
            return null;
        }
    };

    // Helper function to get user email
    const getUserEmail = () => {
        const currentUser = reduxUser || initializeUserData();
        const email = currentUser?.email;
        console.log('Current user:', currentUser); // Debug log
        console.log('User email:', email); // Debug log
        return email || 'your email';
    };

    useEffect(() => {
        // Initialize user data from localStorage if Redux is empty
        if (!reduxUser) {
            initializeUserData();
        }
        fetch2FAStatus();
    }, []);

    const handleToggle2FA = async () => {
        if (isLoading) return;

        // If 2FA is enabled and user wants to disable it, show confirmation modal
        if (is2FAEnabled) {
            setShowDisableModal(true);
            return;
        }

        // If 2FA is disabled and user wants to enable it, proceed directly
        await enable2FADirectly();
    };

    const enable2FADirectly = async () => {
        try {
            setIsLoading(true);
            setError(null);
            setSaveStatus('loading');

            const response = await enable2FA();

            if (response.success) {
                const newStatus = response.data.two_factor_enabled;
                const alwaysRequired = response.data.two_factor_always_required || false;
                setIs2FAEnabled(newStatus);
                setOriginalStatus(newStatus);
                setIsAlwaysRequire(alwaysRequired);
                setSaveStatus('success');

                if (newStatus) {
                    setCopied(true);
                    setTimeout(() => setCopied(false), 3000);
                }
            } else {
                throw new Error(response.message || 'Failed to enable 2FA');
            }
        } catch (err) {
            console.error('2FA enable error:', err);
            setError(err.message || 'Failed to enable 2FA');
            setSaveStatus('error');
        } finally {
            setIsLoading(false);
            setTimeout(() => setSaveStatus(null), 3000);
        }
    };

    const confirmDisable2FA = async () => {
        try {
            setIsLoading(true);
            setError(null);
            setSaveStatus('loading');
            setShowDisableModal(false);

            const response = await disable2FA();

            if (response.success) {
                const newStatus = response.data.two_factor_enabled;
                const alwaysRequired = response.data.two_factor_always_required || false;
                setIs2FAEnabled(newStatus);
                setOriginalStatus(newStatus);
                setIsAlwaysRequire(alwaysRequired);
                setSaveStatus('success');
            } else {
                throw new Error(response.message || 'Failed to disable 2FA');
            }
        } catch (err) {
            console.error('2FA disable error:', err);
            setError(err.message || 'Failed to disable 2FA');
            setSaveStatus('error');
        } finally {
            setIsLoading(false);
            setTimeout(() => setSaveStatus(null), 3000);
        }
    };

    const handleToggleAlwaysRequire = async () => {
        if (!is2FAEnabled || isLoading) return;

        try {
            setIsLoading(true);
            setError(null);
            setSaveStatus('loading');

            const newAlwaysRequired = !isAlwaysRequire;
            const response = await update2FAAlwaysRequired(newAlwaysRequired);

            if (response.success) {
                setIsAlwaysRequire(newAlwaysRequired);
                setSaveStatus('success');
            } else {
                throw new Error(response.message || 'Failed to update always require setting');
            }
        } catch (err) {
            console.error('Error updating always require setting:', err);
            setError(err.message || 'Failed to update always require setting');
            setSaveStatus('error');
        } finally {
            setIsLoading(false);
            setTimeout(() => setSaveStatus(null), 3000);
        }
    };

    const handleGenerateRestoreCode = () => {
        if (!is2FAEnabled) return;
        // Show warning modal first
        setShowGenerateWarningModal(true);
    };

    const confirmGenerateRestoreCode = async () => {
        try {
            setIsGeneratingCode(true);
            setError(null);
            setShowGenerateWarningModal(false);

            const response = await generateRestoreCode();

            if (response.success && response.data.restore_code) {
                setRestoreCode(response.data.restore_code);
                setShowRestoreModal(true);
            } else {
                throw new Error(response.message || 'Failed to generate restore code');
            }
        } catch (err) {
            console.error('Error generating restore code:', err);
            setError(err.message || 'Failed to generate restore code');
        } finally {
            setIsGeneratingCode(false);
        }
    };



    const metaArray = {
        noindex: true,
        title: "Two-Factor Authentication | TradeReply",
        description: "Manage your two-factor authentication settings for enhanced account security on TradeReply.com.",
        canonical_link: "https://www.tradereply.com/account/security/two-factor",
        og_site_name: "TradeReply",
        og_title: "Two-Factor Authentication | TradeReply",
        og_description: "Manage your two-factor authentication settings for enhanced account security on TradeReply.com.",
        twitter_title: "Two-Factor Authentication | TradeReply",
        twitter_description: "Manage your two-factor authentication settings for enhanced account security on TradeReply.com.",
    };

    return (
        <AccountLayout>
            <MetaHead props={metaArray} />
            <div className="security_sec">
                <SidebarHeading title="Two-Factor Verification Setup" />
                <Row>
                    <Col lg={12} xs={12} className="d-flex mb-4">
                        <div className="common_blackcard account_card w-100">
                            <div className="common_blackcard_innerheader mb-3">
                                <div className="common_blackcard_innerheader_content">
                                    <div className="account_header_main" style={{justifySelf: 'baseline'}}>
                                        <h6>Two-Factor Verification</h6>
                                        <div className="account_status_indicator">
                                            <StatusIndicator
                                                saveStatus={saveStatus}
                                                error={error}
                                                loadingText="Updating..."
                                                successText="Settings updated"
                                                defaultText="All changes saved"
                                            />
                                        </div>
                                    </div>
                                    <p className="mb-0 mt-2" style={{ fontWeight: 300, fontSize: '16px' }}>
                                        Prevent unauthorized logins by setting up 2-step email verification.
                                    </p>
                                </div>
                            </div>



                            <div className="account_card_list m-2">
                                {copied && (
                                    <div className="mt-1 mb-2 w-100 text-start">
                                            <span className="green_text" >
                                                Two-Factor Verification is now enabled. Verification codes will be sent to your email <strong>{getUserEmail()}</strong> when required.
                                            </span>
                                    </div>
                                )}
                                <ul className="list-unstyled mb-3">

                                    <li className="mb-3 d-flex align-items-center justify-content-between">
                                        <div className="d-flex align-items-center">
                                            <span className="fw-bold me-2">Status</span>
                                            {isLoadingUserData ? (
                                                <>
                                                    <img
                                                        src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-saving.svg"
                                                        alt="Loading"
                                                        width={20}
                                                        height={20}
                                                    />
                                                    <span className="fw-semibold ms-3" style={{ color: '#00adef' }}>
                                                        Loading...
                                                    </span>
                                                </>
                                            ) : is2FAEnabled ? (
                                                <>
                                                    <CheckIcon />
                                                    <span className="green_text fw-semibold ms-3">Active</span>
                                                </>
                                            ) : (
                                                <>
                                                    <img
                                                        src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-failure.svg"
                                                        alt="Inactive"
                                                        width={20}
                                                        height={20}
                                                    />
                                                    <span className="fw-semibold ms-3" style={{ color: '#9c9a9f' }}>
                                                        Inactive
                                                    </span>
                                                </>
                                            )}
                                        </div>

                                        <label className="switch">
                                            <input
                                                type="checkbox"
                                                checked={is2FAEnabled}
                                                onChange={handleToggle2FA}
                                                disabled={isLoading || isLoadingUserData}
                                            />
                                            <span className="slider"></span>
                                        </label>
                                    </li>

                                    <li>
                                        <div className="d-flex align-items-center gap-2">
                                            <span>Restore Code </span>
                                           
                                        </div>
                                    {is2FAEnabled ? (
                                         <CommonTooltip
                                                    content="Generates a new restoral code in case you've lost the original. Warning: This will permanently overwrite your existing code and make the old one invalid."
                                                    position="top-right"
                                                >
                                            <button
                                                className="restore_code add_number"
                                                type="button"
                                                onClick={handleGenerateRestoreCode}
                                                disabled={isGeneratingCode}
                                            >
                                                {isGeneratingCode ? 'Generating...' : 'Generate Restore Code'}
                                                </button>
                                                </CommonTooltip>
                                        ) : (
                                            <button disabled className="restore_code add_number text-white">
                                                Enable 2FV to unlock restore code
                                            </button>
                                        )}
                                    </li>
                                </ul>

                                <hr className="my-4" />

                                <div className="flex flex-col gap-2">
                                    {/* Header row with title and toggle */}
                                    <div className="flex justify-between items-center w-full">
                                        <h6 className="font-semibold text-base md:text-lg">
                                            Always Require 2-step verification for login
                                        </h6>
                                        <label className="switch shrink-0">
                                            <input
                                                type="checkbox"
                                                checked={isAlwaysRequire}
                                                onChange={handleToggleAlwaysRequire}
                                                disabled={!is2FAEnabled}
                                            />
                                            <span className="slider"></span>
                                        </label>
                                    </div>

                                    {/* Description text */}
                                    <div className="text-sm mt-1">
                                        <p className="mb-1">
                                            If enabled, 2-step verification will be prompted on every login.
                                        </p>
                                        <p className="mb-0">
                                            If disabled, your device will be remembered for 30 days.
                                        </p>
                                    </div>
                                </div>


                            </div>
                        </div>
                    </Col>


                </Row>

                <RestoreCodeModal
                    show={showRestoreModal}
                    handleClose={() => setShowRestoreModal(false)}
                    restoreCode={restoreCode}
                />
                <ConfirmDisable2FAModal
                    show={showDisableModal}
                    handleConfirm={confirmDisable2FA}
                    handleCancel={() => setShowDisableModal(false)}
                />
                <ConfirmGenerateRestoreCodeModal
                    show={showGenerateWarningModal}
                    handleConfirm={confirmGenerateRestoreCode}
                    handleCancel={() => setShowGenerateWarningModal(false)}
                />
            </div>
        </AccountLayout>
    );
}

